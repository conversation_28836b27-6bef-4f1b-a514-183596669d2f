# Borouge ESG Intelligence Platform - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=3001
NODE_ENV=development

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# =============================================================================
# AI PROVIDER API KEYS
# =============================================================================

# Primary AI Engine (Free Tier - 100 requests/day)
GROQ_API_KEY=your_groq_api_key_here
GROQ_BASE_URL=https://api.groq.com/openai/v1
GROQ_MODEL=llama3-8b-8192

# Secondary AI Engine (Free Tier - 900 requests/day)
GEMINI_API_KEY=your_gemini_api_key_here

# Emergency Backup AI Engine (Paid - preserve budget)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
GROQ_RATE_LIMIT=100
GEMINI_RATE_LIMIT=900
OPENAI_RATE_LIMIT=50

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================
CACHE_TTL=86400000
PROVIDER_CACHE_TTL=300000

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================
ENABLE_ANALYTICS=true
ENABLE_MONITORING=true
LOG_LEVEL=info

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
CORS_ORIGIN=http://localhost:3000
API_RATE_LIMIT=100

# =============================================================================
# INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env in the same directory
# 2. Replace all placeholder values with your actual API keys and configuration
# 3. Never commit the .env file to version control
# 4. Ensure all team members have their own .env file with valid credentials
# 5. For production deployment, set these as environment variables in your hosting platform

# =============================================================================
# API KEY SOURCES
# =============================================================================
# Groq API: https://console.groq.com/keys
# Gemini API: https://aistudio.google.com/app/apikey
# OpenAI API: https://platform.openai.com/api-keys
# Supabase: https://supabase.com/dashboard/project/[your-project]/settings/api
