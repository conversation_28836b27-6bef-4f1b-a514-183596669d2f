{"name": "borouge-esg-backend", "version": "1.0.0", "description": "Clean backend for Borouge ESG Intelligence Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["esg", "borouge", "api"], "author": "Borouge Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.49.8", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}