{"name": "borouge-esg-intelligence-backend", "version": "1.0.0", "description": "Backend API for Borouge ESG Intelligence Platform - Production-ready Express.js server with multi-LLM strategy", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "build": "echo \"No build step required for Node.js\"", "health": "curl -f http://localhost:3001/health || exit 1"}, "keywords": ["esg", "intelligence", "borouge", "petrochemical", "sustainability", "api", "express", "supabase", "ai"], "author": "Borouge ESG Intelligence Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/borouge/esg-intelligence-backend"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}