# Borouge ESG Intelligence Platform

A clean, minimal ESG intelligence platform ready for new implementation.

## Project Structure

```
├── Bo_Prompt                    # Master prompt for ESG intelligence (PRESERVED)
├── src/                        # Clean React frontend
├── backend/                    # Simplified Node.js backend
└── public/                     # Static assets
```

## Quick Start

### Frontend
```bash
npm install
npm start
```

### Backend
```bash
cd backend
npm install
npm start
```

## Current State

✅ **Completely cleaned codebase**
✅ **No complex article display logic**
✅ **No follow-up functionality**
✅ **Simple conversation interface**
✅ **Ready for new implementation**

The system has been cleaned and simplified, ready for implementing a new article display approach.
